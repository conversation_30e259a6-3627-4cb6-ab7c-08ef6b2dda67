
import { useState, createContext, useContext } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Login from "./pages/Login";
import Dashboard from "./pages/DashboardLayout";
import DashboardHome from "./pages/DashboardHome";
import Analytics from "./pages/Analytics";
import ElectionAnalyticsPage from "./pages/ElectionAnalyticsPage";
import EarlyWarningPage from "./pages/EarlyWarningPage";
import PreventiveActionsPage from "./pages/PreventiveActionsPage";
import AlertsPage from "./pages/AlertsPage";
import Reports from "./pages/Reports";
import AllReports from "./pages/AllReports";
import SystemUsers from "./pages/SystemUsers";
import SettingsPage from "./pages/SettingsPage";
import PlatformSettings from "./pages/PlatformSettings";
import NotFound from "./pages/NotFound";
import FloatingChat from "./components/FloatingChat";
import "./services/chatApiTest"; // Import test functions

const queryClient = new QueryClient();

// Auth Context
interface AuthContextType {
  isAuthenticated: boolean;
  handleLogin: () => void;
  handleLogout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Protected Route Component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  const authValue = {
    isAuthenticated,
    handleLogin,
    handleLogout,
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={authValue}>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login onLogin={handleLogin} />} />
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Navigate to="/dashboard" replace />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard onLogout={handleLogout} />
                </ProtectedRoute>
              }
            >              <Route index element={<DashboardHome />} />
              <Route path="analytics" element={<Analytics />} />
              <Route path="election-analytics" element={<ElectionAnalyticsPage />} />
              <Route path="early-warning" element={<EarlyWarningPage />} />
              <Route path="preventive-actions" element={<PreventiveActionsPage />} />
              <Route path="reports" element={<Reports />} />
              <Route path="all-reports" element={<AllReports />} />
              <Route path="alerts" element={<AlertsPage />} />
              <Route path="users" element={<SystemUsers />} />
              <Route path="media-profiles" element={<SettingsPage />} />
              <Route path="platform-settings" element={<PlatformSettings />} />
            </Route><Route path="*" element={<NotFound />} />
          </Routes>
          {/* FloatingChat component available on all authenticated pages */}
          {isAuthenticated && <FloatingChat />}
        </BrowserRouter>
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

export default App;
