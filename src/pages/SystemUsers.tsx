
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, UserPlus } from "lucide-react";

const SystemUsers = () => {
  const [selectedRole, setSelectedRole] = useState("All Users");
  const [selectedStatus, setSelectedStatus] = useState("All");

  const userStats = [
    { label: "Total Users", count: 24, color: "border-l-blue-500" },
    { label: "Active Users", count: 18, color: "border-l-green-500" },
    { label: "Pending Approval", count: 4, color: "border-l-orange-500" },
    { label: "Inactive Users", count: 2, color: "border-l-red-500" }
  ];

  const users = [
    {
      name: "Misheck Kamuloni",
      email: "<EMAIL>",
      role: "System Admin",
      department: "IT & Digital",
      lastActive: "2 minutes ago",
      status: "Active",
      avatar: "<PERSON><PERSON>",
      avatarColor: "bg-blue-500"
    },
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Senior Analyst",
      department: "Governance",
      lastActive: "15 minutes ago",
      status: "Active",
      avatar: "AM",
      avatarColor: "bg-green-500"
    },
    {
      name: "John Banda",
      email: "<EMAIL>",
      role: "Data Analyst",
      department: "Research",
      lastActive: "1 hour ago",
      status: "Active",
      avatar: "JB",
      avatarColor: "bg-orange-500"
    },
    {
      name: "Grace Mbewe",
      email: "<EMAIL>",
      role: "Content Moderator",
      department: "Communications",
      lastActive: "3 hours ago",
      status: "Pending",
      avatar: "GM",
      avatarColor: "bg-yellow-500"
    },
    {
      name: "Peter Nyirenda",
      email: "<EMAIL>",
      role: "Viewer",
      department: "Program Management",
      lastActive: "2 days ago",
      status: "Inactive",
      avatar: "PN",
      avatarColor: "bg-gray-500"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Pending":
        return <Badge className="bg-orange-100 text-orange-800">Pending</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "System Admin":
        return "text-red-600";
      case "Senior Analyst":
        return "text-blue-600";
      case "Data Analyst":
        return "text-blue-600";
      case "Content Moderator":
        return "text-purple-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Users Management</h1>
          <p className="text-gray-600 mt-2">Manage user access and permissions</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Filter by Role:</span>
          <select 
            className="p-2 border border-gray-300 rounded-md text-sm"
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
          >
            <option>All Users</option>
            <option>System Admin</option>
            <option>Senior Analyst</option>
            <option>Data Analyst</option>
            <option>Content Moderator</option>
            <option>Viewer</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Status:</span>
          <select 
            className="p-2 border border-gray-300 rounded-md text-sm"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option>All</option>
            <option>Active</option>
            <option>Pending</option>
            <option>Inactive</option>
          </select>
        </div>

        <Button className="bg-blue-600 hover:bg-blue-700 ml-auto">
          <UserPlus className="w-4 h-4 mr-2" />
          Add New User
        </Button>
      </div>

      {/* User Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {userStats.map((stat, index) => (
          <Card key={index} className={`border-l-4 ${stat.color}`}>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-gray-900 mb-1">{stat.count}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* User Management Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            User Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-3">User</th>
                  <th className="text-left p-3">Role</th>
                  <th className="text-left p-3">Department</th>
                  <th className="text-left p-3">Last Active</th>
                  <th className="text-left p-3">Status</th>
                  <th className="text-left p-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="p-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-full ${user.avatarColor} flex items-center justify-center text-white font-medium`}>
                          {user.avatar}
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-3">
                      <span className={`font-medium ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="p-3">{user.department}</td>
                    <td className="p-3">{user.lastActive}</td>
                    <td className="p-3">{getStatusBadge(user.status)}</td>
                    <td className="p-3">
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">Edit</Button>
                        {user.status === "Pending" ? (
                          <>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                              Approve
                            </Button>
                            <Button size="sm" variant="outline">Reject</Button>
                          </>
                        ) : user.status === "Inactive" ? (
                          <>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                              Activate
                            </Button>
                            <Button size="sm" variant="outline">Delete</Button>
                          </>
                        ) : (
                          <Button size="sm" variant="outline">Disable</Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemUsers;
