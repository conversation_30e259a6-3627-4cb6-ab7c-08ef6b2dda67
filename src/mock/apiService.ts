// Mock API service for the Media Analysis Platform

import { electionAnalyticsData, ElectionAnalyticsData } from './electionAnalyticsMock';
import { earlyWarningSystemData, EarlyWarningSystemData } from './earlyWarningSystemMock';
import { preventiveActionsData, PreventiveActionsData } from './preventiveActionsMock';

// Simulate API response delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// API service class
export class ApiService {
  // Election Analytics
  static async getElectionAnalytics(): Promise<ElectionAnalyticsData> {
    await delay(800); // Simulate network delay
    return electionAnalyticsData;
  }

  static async getCandidateDetails(candidateId: string) {
    await delay(600);
    const candidate = electionAnalyticsData.candidateData.find(c => c.id === candidateId);
    if (!candidate) {
      throw new Error('Candidate not found');
    }
    return {
      ...candidate,
      detailedData: {
        recentStatements: [
          { date: "2025-06-01", content: "Statement on economic policy plans for the next term" },
          { date: "2025-05-28", content: "Response to corruption allegations from opposing candidates" },
          { date: "2025-05-25", content: "Announcement of rural development initiatives" }
        ],
        mediaAppearances: [
          { date: "2025-06-02", channel: "MBC TV", program: "Candidate Interview" },
          { date: "2025-05-30", channel: "Capital Radio", program: "Morning Debate" },
          { date: "2025-05-26", channel: "Times Newspaper", program: "Front Page Profile" }
        ],
        supportDemographics: {
          age: [
            { group: "18-24", percentage: 28 },
            { group: "25-34", percentage: 35 },
            { group: "35-44", percentage: 22 },
            { group: "45-64", percentage: 10 },
            { group: "65+", percentage: 5 }
          ],
          gender: [
            { group: "Male", percentage: 55 },
            { group: "Female", percentage: 45 }
          ],
          region: [
            { name: "Northern", percentage: 25 },
            { name: "Central", percentage: 45 },
            { name: "Southern", percentage: 30 }
          ]
        }
      }
    };
  }

  static async getNarrativeDetails(narrativeId: string) {
    await delay(600);
    const narrative = electionAnalyticsData.narrativeData.find(n => n.id === narrativeId);
    if (!narrative) {
      throw new Error('Narrative not found');
    }
    return {
      ...narrative,
      detailedData: {
        recentExamples: [
          { date: "2025-06-02", platform: "Facebook", content: "Example post content about this narrative" },
          { date: "2025-06-01", platform: "Twitter/X", content: "Another example related to this narrative" },
          { date: "2025-05-30", platform: "TikTok", content: "Video content discussing this narrative" }
        ],
        relatedHashtags: [
          { tag: "#ElectionFairness", count: 1245 },
          { tag: "#TransparentElection", count: 876 },
          { tag: "#VoterRights", count: 543 }
        ],
        influencers: [
          { name: "Media Outlet A", followers: 120000, impact: "High" },
          { name: "Political Commentator B", followers: 85000, impact: "Medium" },
          { name: "Civil Society Org C", followers: 65000, impact: "High" }
        ]
      }
    };
  }

  // Early Warning System
  static async getEarlyWarningSystemData(): Promise<EarlyWarningSystemData> {
    await delay(800);
    return earlyWarningSystemData;
  }

  static async getAlertDetails(alertId: string) {
    await delay(600);
    const alert = earlyWarningSystemData.recentAlerts.find(a => a.id === alertId);
    if (!alert) {
      throw new Error('Alert not found');
    }
    return {
      ...alert,
      detailedData: {
        relatedIncidents: [
          { id: "inc123", title: "Similar incident in different location", date: "2025-05-30" },
          { id: "inc124", title: "Previous occurrence of this type", date: "2025-05-25" }
        ],
        responseActions: [
          { action: "Alert verification team deployed", timestamp: "2025-06-03T09:50:00Z", status: "Completed" },
          { action: "Local authorities notified", timestamp: "2025-06-03T10:05:00Z", status: "Completed" },
          { action: "Public clarification message drafted", timestamp: "2025-06-03T10:30:00Z", status: "In Progress" }
        ],
        evidenceSources: [
          { type: "Social Media Posts", count: 24, firstDetected: "2025-06-03T09:30:00Z" },
          { type: "Media Reports", count: 2, firstDetected: "2025-06-03T09:45:00Z" },
          { type: "Community Reports", count: 5, firstDetected: "2025-06-03T10:00:00Z" }
        ],
        map: {
          latitude: -13.9626,
          longitude: 33.7741,
          zoom: 12
        }
      }
    };
  }

  static async submitAlertResponse(alertId: string, response: { action: string, notes: string }) {
    await delay(500);
    return { success: true, message: "Response submitted successfully" };
  }

  // Preventive Actions
  static async getPreventiveActionsData(): Promise<PreventiveActionsData> {
    await delay(800);
    return preventiveActionsData;
  }

  static async getActionDetails(actionId: string) {
    await delay(600);
    const action = preventiveActionsData.actions.find(a => a.id === actionId);
    if (!action) {
      throw new Error('Action not found');
    }
    return {
      ...action,
      detailedData: {
        timeline: [
          { date: action.createdDate, event: "Action created", user: "System Admin" },
          { date: "2025-05-20", event: "Action assigned", user: action.assignedTo },
          { date: "2025-05-25", event: "Status updated to In Progress", user: action.assignedTo },
          { date: "2025-06-01", event: "Progress update: 65% completed", user: action.assignedTo }
        ],
        relatedResources: preventiveActionsData.resources.slice(0, 2),
        notes: [
          { date: "2025-06-01", user: action.assignedTo, content: "Contacted platform representatives, waiting for response" },
          { date: "2025-05-25", user: "System Admin", content: "Priority upgraded to Critical based on recent events" }
        ],
        dependencies: [
          { id: "dep1", title: "Establish technical infrastructure", status: "Completed" },
          { id: "dep2", title: "Secure budget approval", status: "Completed" }
        ]
      }
    };
  }

  static async updateActionStatus(actionId: string, update: { status: string, completionPercentage: number, notes: string }) {
    await delay(500);
    return { success: true, message: "Action updated successfully" };
  }

  static async createNewAction(actionData: Partial<PreventiveActionsData['actions'][0]>) {
    await delay(800);
    return { 
      success: true, 
      message: "New action created successfully", 
      actionId: `pa${Math.floor(Math.random() * 1000)}` 
    };
  }
}

// API endpoints definition (for documentation purposes)
export const API_ENDPOINTS = {
  // Election Analytics
  ELECTION_ANALYTICS: "/api/election-analytics",
  CANDIDATE_DETAILS: "/api/candidate/:candidateId",
  NARRATIVE_DETAILS: "/api/narrative/:narrativeId",
  
  // Early Warning System
  EARLY_WARNING_SYSTEM: "/api/early-warning-system",
  ALERT_DETAILS: "/api/alert/:alertId",
  ALERT_RESPONSE: "/api/alert/:alertId/response",
  
  // Preventive Actions
  PREVENTIVE_ACTIONS: "/api/preventive-actions",
  ACTION_DETAILS: "/api/action/:actionId",
  UPDATE_ACTION: "/api/action/:actionId",
  CREATE_ACTION: "/api/action/create"
};
