
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Download, FileText, TrendingUp, BarChart3, AlertTriangle } from "lucide-react";
import { MonitoredIncidents } from "@/components/MonitoredIncidents";

const Reports = () => {
  const [selectedReport, setSelectedReport] = useState("Daily Summary");

  const reportTypes = [
    "Daily Summary",
    "Weekly Analysis",
    "Monthly Overview",
    "Custom Range"
  ];

  const scheduledReports = [
    {
      name: "Daily Monitoring Summary",
      schedule: "Daily at 6:00 AM",
      recipients: "5 recipients",
      lastSent: "Today, 6:00 AM",
      status: "Active"
    },
    {
      name: "Weekly Analytics Report",
      schedule: "Weekly on Monday",
      recipients: "3 recipients",
      lastSent: "May 27, 2025",
      status: "Active"
    },
    {
      name: "Executive Monthly Brief",
      schedule: "Monthly on 1st",
      recipients: "2 recipients",
      lastSent: "May 1, 2025",
      status: "Active"
    },
    {
      name: "Threat Assessment Report",
      schedule: "Bi-weekly",
      recipients: "8 recipients",
      lastSent: "May 15, 2025",
      status: "Pending"
    }
  ];
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Reports & Export</h1>
        <p className="text-gray-600 mt-2">Generate and schedule comprehensive reports</p>
      </div>      <Tabs defaultValue="incidents" className="w-full">
        <TabsList className="grid w-full grid-cols-1">
          <TabsTrigger value="incidents" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            Monitored Incidents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="reports" className="space-y-6 mt-6">
          {/* Advanced Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-600 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Advanced Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label>Report Type:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Daily Summary</option>
                    <option>Weekly Analysis</option>
                    <option>Monthly Overview</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Platform:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                    <option>Facebook</option>
                    <option>Twitter/X</option>
                    <option>TikTok</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Date Range:</Label>
                  <div className="flex space-x-2">
                    <Input type="date" defaultValue="2025-05-22" />
                    <span className="self-center">to</span>
                    <Input type="date" defaultValue="2025-05-29" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Candidate Names:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Political Parties/CSOs/Movements:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Prominent Names:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Hashtags:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Story Highlights:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Frequent Words/Phrases:</Label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option>Select options...</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-4 mt-6">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Apply Filters & Generate Report
                </Button>
                <Button variant="outline">
                  Reset Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Report Previews */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Daily Summary Report
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Date Range:</span>
                    <span className="text-sm">May 29, 2025</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Posts:</span>
                    <span className="text-sm">3,456</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Alerts Generated:</span>
                    <span className="text-sm">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Avg. Response Time:</span>
                    <span className="text-sm">4.2 min</span>
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button size="sm" variant="outline">PDF</Button>
                  <Button size="sm" variant="outline">Excel</Button>
                  <Button size="sm" variant="outline">CSV</Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Weekly Analysis Report
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Date Range:</span>
                    <span className="text-sm">May 22-29, 2025</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Posts:</span>
                    <span className="text-sm">24,567</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Trend Analysis:</span>
                    <Badge className="bg-green-100 text-green-800">Positive</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Key Insights:</span>
                    <span className="text-sm">8 findings</span>
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button size="sm" variant="outline">PDF</Button>
                  <Button size="sm" variant="outline">Excel</Button>
                  <Button size="sm" variant="outline">CSV</Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Executive Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Period:</span>
                    <span className="text-sm">May 2025</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Key Insights:</span>
                    <span className="text-sm">15 pages</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Stakeholder Ready:</span>
                    <Badge className="bg-green-100 text-green-800">Yes</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Risk Level:</span>
                    <Badge className="bg-orange-100 text-orange-800">Medium</Badge>
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button size="sm" variant="outline">PDF</Button>
                  <Button size="sm" variant="outline">PowerPoint</Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Scheduled Reports */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Scheduled Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-blue-50">
                      <th className="text-left p-3">Report Name</th>
                      <th className="text-left p-3">Schedule</th>
                      <th className="text-left p-3">Recipients</th>
                      <th className="text-left p-3">Last Sent</th>
                      <th className="text-left p-3">Status</th>
                      <th className="text-left p-3">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {scheduledReports.map((report, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-3 font-medium">{report.name}</td>
                        <td className="p-3">{report.schedule}</td>
                        <td className="p-3">{report.recipients}</td>
                        <td className="p-3">{report.lastSent}</td>
                        <td className="p-3">
                          <Badge 
                            className={
                              report.status === "Active" 
                                ? "bg-green-100 text-green-800" 
                                : "bg-orange-100 text-orange-800"
                            }
                          >
                            {report.status}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <Button size="sm" variant="outline">Edit</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="incidents" className="mt-6">
          <MonitoredIncidents />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;
