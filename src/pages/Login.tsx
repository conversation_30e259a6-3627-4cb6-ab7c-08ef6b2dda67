
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";

interface LoginProps {
  onLogin: () => void;
}

const Login = ({ onLogin }: LoginProps) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);    // Mock authentication - check for admin credentials
    if (username === "admin" && password === "admin123") {
      setTimeout(() => {
        toast({
          title: "Login Successful",
          description: "Welcome to Magwero Media Monitoring System",
        });
        onLogin();
        navigate("/dashboard");
        setIsLoading(false);
      }, 1000);
    } else {
      setTimeout(() => {
        toast({
          title: "Login Failed",
          description: "Invalid credentials. Use admin/admin123",
          variant: "destructive",
        });
        setIsLoading(false);
      }, 1000);
    }
  };
  return (
    <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="bg-white rounded-lg shadow-xl border-2 border-yellow-400">
          <CardContent className="p-8">            {/* Logos Section */}
            <div className="flex justify-center mb-8">
              <div className="p-2 bg-white rounded-lg shadow-lg">
                <div className="flex items-center justify-center">
                  <img 
                    src="/lovable-uploads/3e2b6588-d21b-42b6-8b8f-30cf6f521314.png" 
                    alt="UNDP and MAGWERO logos" 
                    className="w-[15rem] object-contain"
           
                  />
                </div>
              </div>
            </div>            {/* Title */}
            <div className="text-center mb-8">
              <h1 className="text-gray-600 text-lg font-normal">Media Analysis Platform for Election</h1>
            </div>            <form onSubmit={handleLogin} className="space-y-6">              <div className="space-y-2">
                <Label htmlFor="username" className="text-gray-700 font-bold text-lg">Username</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Enter your username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  className="h-12 border-gray-200 rounded-md focus:ring-blue-600 focus:border-blue-600 font-normal"
                />
              </div>
                <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 font-bold text-lg">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-12 border-gray-200 rounded-md focus:ring-blue-600 focus:border-blue-600 font-normal"
                />
              </div>
                <Button 
                type="submit" 
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-normal rounded-md"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </form>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-gray-400 text-sm">Powered by UNDP Malawi</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Login;
